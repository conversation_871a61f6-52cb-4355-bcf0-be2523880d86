package cloud.demand.lab.modules.operation_view.inventory_health.enums;

/**
 * 预扣类型
 * {
 *             fault_reserved: "故障预留",
 *             not_reserved: "not_reserved",
 *             reserved_from_host: "普通预留",
 *             reserved_longtime: "长期预留",
 *             reserved_onetime: "单次预留"
 *         }
 */
public enum YunxiaoGridReserveModeEnum {
    FAULT_RESERVED("fault_reserved", "故障预留"),
    NOT_RESERVED("not_reserved", "not_reserved"),
    RESERVED_FROM_HOST("reserved_from_host", "普通预留"),
    RESERVED_LONGTIME("reserved_longtime", "长期预留"),
    RESERVED_ONETIME("reserved_onetime", "单次预留");
    private String code;
    private String name;

    YunxiaoGridReserveModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String getNameFromCode(String code) {
        for (YunxiaoGridReserveModeEnum value : YunxiaoGridReserveModeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}
