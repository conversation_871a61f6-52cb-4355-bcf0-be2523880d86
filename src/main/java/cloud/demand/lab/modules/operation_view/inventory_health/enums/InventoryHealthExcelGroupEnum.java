package cloud.demand.lab.modules.operation_view.inventory_health.enums;

import cloud.demand.lab.common.excel.core.ExcelGroup;
import cloud.demand.lab.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;

public enum InventoryHealthExcelGroupEnum implements ExcelGroup {
    SERVICE_LEVEL_GROUP_ENUM(InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_SERVICE_LEVEL,
            "服务水平目标配置组","excel/inventory-health/target_service_level.xlsx"),
    ZONE_GROUP_ENUM(InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_ZONE,
            "服务水平目标配置组", "excel/inventory-health/zone_type.xlsx"),
    INSTANCE_TYPE_ENUM(InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_INSTANCE_TYPE,
            "服务水平目标配置组", "excel/inventory-health/type_of_instance_type.xlsx"),
    BUFFER_POOL_ENUM(InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL,
            "弹性备货池策略", "excel/inventory-health/buffer_pool_config.xlsx"),
    INSTANCE_MODEL_CONFIG(InventoryHealthExcelGroup.INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG, "实例规格占比调整", "excel/inventory-health/inv-instance-model-config.xlsx");

    private final String code;

    private final String remark;

    private final String templatePath;

    InventoryHealthExcelGroupEnum(String code, String remark, String templatePath) {
        this.code = code;
        this.remark = remark;
        this.templatePath = templatePath;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public String getTemplatePath() {
        return templatePath;
    }
}
